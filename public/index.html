<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ForaChat - Work Squad</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: relative;
        }



        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: #007AFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .back-arrow {
            font-size: 18px;
        }

        .group-info h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .group-info p {
            font-size: 12px;
            opacity: 0.8;
        }

        .status {
            font-size: 11px;
            opacity: 0.9;
        }

        .status.success { color: #34C759; }
        .status.error { color: #FF3B30; }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fff;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            margin-left: auto;
        }

        .message.user .bubble {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 8px;
        }

        .message.assistant .bubble,
        .message.fora .bubble,
        .message.jan .bubble,
        .message.lou .bubble {
            background: #E5E5EA;
            color: #000;
        }

        .message:not(.user) {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 18px;
        }

        .avatar.fora { background: #FF3B30; }
        .avatar.jan { background: #34C759; }
        .avatar.lou { background: #FF9500; }

        .message-content {
            flex: 1;
            max-width: calc(100% - 38px);
        }

        .message:not(.user) .bubble {
            border-bottom-left-radius: 8px;
        }

        .message.user .message-content {
            max-width: 80%;
        }

        .sender-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #8E8E93;
        }

        .bubble {
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .timestamp {
            font-size: 11px;
            color: #8E8E93;
            margin-top: 4px;
            text-align: center;
        }

        .message.user .timestamp {
            text-align: right;
        }

        .user .sender-name {
            display: none;
        }

        .message:not(.user) .timestamp {
            text-align: left;
        }

        .message.system {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
            max-width: 100%;
            padding: 12px 16px;
            border-radius: 20px;
            margin: 15px auto;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            max-width: 100%;
            padding: 12px 16px;
            border-radius: 20px;
            margin: 15px auto;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            margin-bottom: 15px;
            padding-left: 12px;
        }

        .typing-indicator.show {
            display: flex;
            animation: fadeIn 0.3s ease-in;
        }

        .typing-dots {
            background: #E5E5EA;
            border-radius: 20px;
            padding: 12px 16px;
            display: flex;
            gap: 4px;
        }

        .dot {
            width: 8px;
            height: 8px;
            background: #8E8E93;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .dot:nth-child(2) { animation-delay: 0.2s; }
        .dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .input-area {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e5e5ea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            background: white;
            border: 1px solid #e5e5ea;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 16px;
            outline: none;
        }

        .input-field:focus {
            border-color: #007AFF;
        }

        .input-field:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
        }

        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
        }

        .send-btn:disabled {
            background: #8E8E93;
            cursor: not-allowed;
        }

        .skills {
            margin: 15px auto;
            padding: 8px 12px;
            background: #f0f8ff;
            border-radius: 12px;
            font-size: 0.9em;
            color: #0066cc;
            text-align: center;
            max-width: 100%;
        }

        /* Responsive design for larger screens */
        @media (min-width: 768px) {
            .phone-container {
                width: 400px;
                height: 700px;
            }
        }

        @media (min-width: 1024px) {
            body {
                padding: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="header">
                <div class="header-left">
                    <span class="back-arrow">‹</span>
                    <div class="group-info">
                        <h3>Work Squad</h3>
                        <p>Fora, Jan, Lou, You</p>
                        <div id="connection-status" class="status">Connecting...</div>
                    </div>
                </div>
                <div>📞 📹</div>
            </div>

            <div class="chat-container" id="messages">
                <div class="message system">
                    Welcome! Type a message to start chatting. Messages will appear with realistic delays.
                </div>
            </div>

            <div class="typing-indicator" id="typing-indicator">
                <div class="typing-dots">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                </div>
            </div>

            <div class="input-area">
                <input
                    type="text"
                    id="message-input"
                    class="input-field"
                    placeholder="Message"
                    disabled
                >
                <button id="send-btn" class="send-btn" disabled>➤</button>
            </div>
        </div>
    </div>

    <script>
        class DelayedChatUI {
            constructor() {
                this.ws = null;
                this.messageInput = document.getElementById('message-input');
                this.messagesContainer = document.getElementById('messages');
                this.connectionStatus = document.getElementById('connection-status');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.sendBtn = document.getElementById('send-btn');
                this.isStreaming = false;

                this.setupEventListeners();
                this.connect();
            }

            setupEventListeners() {
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.sendBtn.addEventListener('click', () => {
                    this.sendMessage();
                });
            }
            
            connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.updateConnectionStatus('Connected', 'success');
                    this.messageInput.disabled = false;
                    this.sendBtn.disabled = false;
                    this.messageInput.focus();
                };

                this.ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                };

                this.ws.onclose = () => {
                    this.updateConnectionStatus('Disconnected', 'error');
                    this.messageInput.disabled = true;
                    this.sendBtn.disabled = true;
                    setTimeout(() => this.connect(), 3000);
                };

                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.updateConnectionStatus('Connection Error', 'error');
                };
            }
            
            updateConnectionStatus(text, type) {
                this.connectionStatus.textContent = text;
                this.connectionStatus.className = `status ${type}`;
            }
            
            sendMessage() {
                const text = this.messageInput.value.trim();
                if (!text || !this.ws || this.ws.readyState !== WebSocket.OPEN) return;
                
                // Add user message to UI
                this.addMessage('user', 'You', text);
                
                // Send to server
                if (this.isStreaming) {
                    this.ws.send(JSON.stringify({ type: 'interrupt', text }));
                } else {
                    this.ws.send(JSON.stringify({ type: 'chat', text }));
                }
                
                this.messageInput.value = '';
                this.showTyping();
            }
            
            handleMessage(message) {
                switch (message.type) {
                    case 'connected':
                        console.log('Connected with session ID:', message.sessionId);
                        break;
                        
                    case 'chat_start':
                        this.isStreaming = true;
                        this.addMessage('system', 'System', `--- ${message.theme} ---`);
                        break;
                        
                    case 'message':
                        this.addMessage('assistant', message.character, message.text);
                        break;
                        
                    case 'chat_complete':
                        this.isStreaming = false;
                        this.hideTyping();
                        if (message.skills && message.skills.length > 0) {
                            this.addSkills(message.skills);
                        }
                        break;
                        
                    case 'interrupted':
                        this.addMessage('system', 'System', message.message);
                        break;

                    case 'extended_workflow_start':
                        this.addMessage('system', 'System', message.message);
                        break;

                    case 'extended_workflow_message':
                        this.addMessage('assistant', message.character, message.text);
                        break;

                    case 'extended_workflow_end':
                        this.addMessage('system', 'System', message.message);
                        break;

                    case 'extended_workflow_extended':
                        this.addMessage('system', 'System', message.message);
                        break;

                    case 'autonomous_message':
                        this.addMessage('assistant', message.character, message.text);
                        break;

                    case 'error':
                        this.isStreaming = false;
                        this.hideTyping();
                        this.addMessage('error', 'Error', `${message.error}${message.details ? ': ' + message.details : ''}`);
                        break;
                }
            }
            
            addMessage(type, character, text) {
                const messageDiv = document.createElement('div');
                const now = new Date();
                const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                if (type === 'user') {
                    messageDiv.className = 'message user';
                    messageDiv.innerHTML = `
                        <div class="message-content">
                            <div class="bubble">${text}</div>
                            <div class="timestamp">${timestamp}</div>
                        </div>
                    `;
                } else if (type === 'assistant') {
                    const characterLower = character.toLowerCase();
                    messageDiv.className = `message ${characterLower}`;
                    const avatarLetter = character.charAt(0).toUpperCase();

                    messageDiv.innerHTML = `
                        <div class="avatar ${characterLower}">${avatarLetter}</div>
                        <div class="message-content">
                            <div class="sender-name">${character}</div>
                            <div class="bubble">${text}</div>
                            <div class="timestamp">${timestamp}</div>
                        </div>
                    `;
                } else if (type === 'system') {
                    messageDiv.className = 'message system';
                    messageDiv.innerHTML = `<div class="bubble">${text}</div>`;
                } else if (type === 'error') {
                    messageDiv.className = 'message error';
                    messageDiv.innerHTML = `<div class="bubble">${text}</div>`;
                }

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            addSkills(skills) {
                const skillsDiv = document.createElement('div');
                skillsDiv.className = 'skills';
                skillsDiv.textContent = `Skills: ${skills.join(', ')}`;
                this.messagesContainer.appendChild(skillsDiv);
                this.scrollToBottom();
            }
            
            showTyping() {
                this.typingIndicator.classList.add('show');
            }
            
            hideTyping() {
                this.typingIndicator.classList.remove('show');
            }
            
            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }
        
        // Initialize the chat UI when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DelayedChatUI();
        });
    </script>
</body>
</html>
